**PR 5: Server Backend - Core Services (E2, E6 Basics) & Ktor Routes**

*   **Assignee:** Alex
*   **Reviewer:** <PERSON>, <PERSON>
*   **Description:** Implement the backend Service layer methods corresponding to basic Session (E2.S1, E2.S3, E2.S4, E6.S1 assign), Group (E6.S3, E6.S4, E6.S6 delete logic), and Credential Status Check (E5.S4) features. These services orchestrate calls to the DAOs, use the `TransactionScope`, and handle errors using Arrow `Either`. Set up the corresponding Ktor API routes (`/api/v1/sessions`, `/api/v1/sessions/{id}`, `/api/v1/sessions/{id}/group`, `/api/v1/groups`, `/api/v1/groups/{id}`, `/api/v1/models/{id}/apikey/status`) in `ApiRoutes.kt` to call these service methods, handling request/response serialization and mapping service results/errors to appropriate HTTP responses.
*   **Stories Addressed:** E2.S1, E2.S3, E2.S4 backend, E6.S1 backend, E6.S3, E6.S4, E6.S6 backend, E5.S4 backend, E1.S4 (LLMClient interface/stub), E7.S3 (Ktor routing setup), E7.S6 (coroutines in services/routes).
*   **Key Files:**
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/SessionService.kt`
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/impl/SessionServiceImpl.kt`
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/GroupService.kt`
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/impl/GroupServiceImpl.kt`
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/ModelService.kt`
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/impl/ModelServiceImpl.kt`
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/MessageService.kt` (Interface placeholder)
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/impl/MessageServiceImpl.kt` (Stubbed placeholder)
*   `server/src/main/kotlin/eu/torvian/chatbot/server/external/llm/LLMApiClient.kt` (Interface)
*   `server/src/main/kotlin/eu/torvian/chatbot/server/external/llm/LLMApiClientKtor.kt` (Stubbed implementation for S1)
*   `server/src/main/kotlin/eu/torvian/chatbot/server/api/server/ApiRoutes.kt`
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/error/*Error.kt` (New Service-level error types)


**1. Service Error Type Files (in new sub-packages)**

Create the following files under `server/src/main/kotlin/eu/torvian/chatbot/server/service/error/`:

**`session/CreateSessionError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.session

/**
 * Represents possible errors during the creation of a chat session.
 */
sealed interface CreateSessionError {
    /**
     * Indicates that the provided name is invalid (e.g., blank).
     */
    data class InvalidName(val reason: String) : CreateSessionError
    /**
     * Indicates that a foreign key constraint failed during insertion (e.g., groupId, modelId, settingsId not found).
     * Maps from SessionError.ForeignKeyViolation in the DAO layer.
     */
    data class InvalidRelatedEntity(val message: String) : CreateSessionError
}
```

**`session/GetSessionDetailsError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.session

/**
 * Represents possible errors when retrieving detailed information for a chat session.
 */
sealed interface GetSessionDetailsError {
    /**
     * Indicates that the session with the specified ID was not found.
     * Maps from SessionError.SessionNotFound in the DAO layer.
     */
    data class SessionNotFound(val id: Long) : GetSessionDetailsError
}
```

**`UpdateSessionNameError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.session
/**
 * Represents possible errors when updating a chat session's name.
 */
sealed interface UpdateSessionNameError {
    /**
     * Indicates that the session with the specified ID was not found.
     * Maps from SessionError.SessionNotFound in the DAO layer.
     */
    data class SessionNotFound(val id: Long) : UpdateSessionNameError
    /**
     * Indicates that the provided new name is invalid (e.g., blank).
     */
    data class InvalidName(val reason: String) : UpdateSessionNameError
}
```

**`UpdateSessionGroupIdError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.session
/**
 * Represents possible errors when assigning or unassigning a session to/from a group.
 */
sealed interface UpdateSessionGroupIdError {
    /**
     * Indicates that the session with the specified ID was not found.
     * Maps from SessionError.SessionNotFound in the DAO layer.
     */
    data class SessionNotFound(val id: Long) : UpdateSessionGroupIdError
    /**
     * Indicates that the target group with the specified ID was not found when assigning.
     * Maps from SessionError.ForeignKeyViolation in the DAO layer.
     */
    data class InvalidRelatedEntity(val message: String) : UpdateSessionGroupIdError
}
```

**`UpdateSessionCurrentModelIdError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.session
/**
 * Represents possible errors when updating a chat session's current model ID.
 */
sealed interface UpdateSessionCurrentModelIdError {
    /**
     * Indicates that the session with the specified ID was not found.
     * Maps from SessionError.SessionNotFound in the DAO layer.
     */
    data class SessionNotFound(val id: Long) : UpdateSessionCurrentModelIdError
    /**
     * Indicates that the provided model ID was not found.
     * Maps from SessionError.ForeignKeyViolation in the DAO layer.
     */
    data class InvalidRelatedEntity(val message: String) : UpdateSessionCurrentModelIdError
}
```

**`UpdateSessionCurrentSettingsIdError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.session
/**
 * Represents possible errors when updating a chat session's current settings ID.
 */
sealed interface UpdateSessionCurrentSettingsIdError {
    /**
     * Indicates that the session with the specified ID was not found.
     * Maps from SessionError.SessionNotFound in the DAO layer.
     */
    data class SessionNotFound(val id: Long) : UpdateSessionCurrentSettingsIdError
    /**
     * Indicates that the provided settings ID was not found.
     * Maps from SessionError.ForeignKeyViolation in the DAO layer.
     */
    data class InvalidRelatedEntity(val message: String) : UpdateSessionCurrentSettingsIdError
}
```

**`UpdateSessionLeafMessageIdError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.session
/**
 * Represents possible errors when updating a chat session's current leaf message ID.
 */
sealed interface UpdateSessionLeafMessageIdError {
    /**
     * Indicates that the session with the specified ID was not found.
     * Maps from SessionError.SessionNotFound in the DAO layer.
     */
    data class SessionNotFound(val id: Long) : UpdateSessionLeafMessageIdError
    /**
     * Indicates that the provided message ID was not found.
     * Maps from SessionError.ForeignKeyViolation in the DAO layer.
     */
    data class InvalidRelatedEntity(val message: String) : UpdateSessionLeafMessageIdError
}
```

**`session/DeleteSessionError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.session

/**
 * Represents possible errors when deleting a chat session.
 */
sealed interface DeleteSessionError {
    /**
     * Indicates that the session with the specified ID was not found.
     * Maps from SessionError.SessionNotFound in the DAO layer.
     */
    data class SessionNotFound(val id: Long) : DeleteSessionError
}
```

---

**`group/CreateGroupError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.group

/**
 * Represents possible errors during the creation of a chat group.
 */
sealed interface CreateGroupError {
    /**
     * Indicates that the provided name is invalid (e.g., blank).
     */
    data class InvalidName(val reason: String) : CreateGroupError
}
```

**`group/RenameGroupError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.group

/**
 * Represents possible errors when renaming a chat group.
 */
sealed interface RenameGroupError {
    /**
     * Indicates that the group with the specified ID was not found.
     * Maps from GroupError.GroupNotFound in the DAO layer.
     */
    data class GroupNotFound(val id: Long) : RenameGroupError
    /**
     * Indicates that the provided new name is invalid (e.g., blank).
     */
    data class InvalidName(val reason: String) : RenameGroupError
}
```

**`group/DeleteGroupError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.group

/**
 * Represents possible errors when deleting a chat group.
 */
sealed interface DeleteGroupError {
    /**
     * Indicates that the group with the specified ID was not found.
     * Maps from GroupError.GroupNotFound in the DAO layer.
     */
    data class GroupNotFound(val id: Long) : DeleteGroupError
}
```

---

**`GetModelError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.model

/**
 * Represents possible errors when retrieving an LLM model by ID.
 */
sealed interface GetModelError {
    /**
     * Indicates that the model with the specified ID was not found.
     * Maps from ModelError.ModelNotFound in the DAO layer.
     */
    data class ModelNotFound(val id: Long) : GetModelError
}
```

**`model/AddModelError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.model

/**
 * Represents possible errors when adding a new LLM model.
 */
sealed interface AddModelError {
    /**
     * Indicates invalid input data for the model (e.g., name, baseUrl, type format).
     * This would typically be a business rule validation failure.
     */
    data class InvalidInput(val reason: String) : AddModelError
}
```

**`model/UpdateModelError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.model

/**
 * Represents possible errors when updating an existing LLM model.
 */
sealed interface UpdateModelError {
    /**
     * Indicates that the model with the specified ID was not found.
     * Maps from ModelError.ModelNotFound in the DAO layer.
     */
    data class ModelNotFound(val id: Long) : UpdateModelError
    /**
     * Indicates invalid input data for the update (e.g., name, baseUrl, type format).
     * This would typically be a business rule validation failure.
     */
    data class InvalidInput(val reason: String) : UpdateModelError
    /**
     * Indicates an error occurred while updating or deleting the API key.
     * Maps from ApiSecretError or CredentialManager specific errors.
     */
    data class CredentialError(val message: String) : UpdateModelError
}
```

**`model/DeleteModelError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.model

/**
 * Represents possible errors when deleting an LLM model.
 */
sealed interface DeleteModelError {
    /**
     * Indicates that the model with the specified ID was not found.
     * Maps from ModelError.ModelNotFound in the DAO layer.
     */
    data class ModelNotFound(val id: Long) : DeleteModelError
    /**
     * Indicates an error occurred while deleting the associated API key.
     * Maps from ApiSecretError or CredentialManager specific errors.
     */
    data class CredentialDeletionError(val message: String) : DeleteModelError
}
```

---

**`settings/GetSettingsByIdError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.settings

/**
 * Represents possible errors when retrieving a settings profile by ID.
 */
sealed interface GetSettingsByIdError {
    /**
     * Indicates that the settings profile with the specified ID was not found.
     * Maps from SettingsError.SettingsNotFound in the DAO layer.
     */
    data class SettingsNotFound(val id: Long) : GetSettingsByIdError
}
```

**`settings/AddSettingsError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.settings

/**
 * Represents possible errors when adding a new settings profile.
 */
sealed interface AddSettingsError {
     /**
      * Indicates that the associated model for the settings profile was not found.
      * Maps from SettingsError.ModelNotFound in the DAO layer.
      */
     data class ModelNotFound(val modelId: Long) : AddSettingsError
     /**
      * Indicates invalid input data for the settings (e.g., name blank, temperature out of range).
      * This would typically be a business rule validation failure.
      */
     data class InvalidInput(val reason: String) : AddSettingsError
}
```

**`settings/UpdateSettingsError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.settings

/**
 * Represents possible errors when updating an existing settings profile.
 */
sealed interface UpdateSettingsError {
    /**
     * Indicates that the settings profile with the specified ID was not found.
     * Maps from SettingsError.SettingsNotFound in the DAO layer.
     */
    data class SettingsNotFound(val id: Long) : UpdateSettingsError
    /**
     * Indicates invalid input data for the update (e.g., name blank, temperature out of range).
     * This would typically be a business rule validation failure.
     */
    data class InvalidInput(val reason: String) : UpdateSettingsError
}
```

**`settings/DeleteSettingsError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.settings

/**
 * Represents possible errors when deleting a settings profile.
 */
sealed interface DeleteSettingsError {
    /**
     * Indicates that the settings profile with the specified ID was not found.
     * Maps from SettingsError.SettingsNotFound in the DAO layer.
     */
    data class SettingsNotFound(val id: Long) : DeleteSettingsError
}
```

---

**`message/ProcessNewMessageError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.message

/**
 * Represents possible errors during the process of receiving and responding to a new user message.
 */
sealed interface ProcessNewMessageError {
    /**
     * Indicates that the session the message belongs to was not found.
     * Maps from SessionError.SessionNotFound in the DAO layer.
     */
    data class SessionNotFound(val sessionId: Long) : ProcessNewMessageError
    /**
     * Indicates that the parent message specified for a reply was not found.
     * Maps from MessageError.MessageNotFound in the DAO layer.
     */
    data class ParentMessageNotFound(val parentId: Long) : ProcessNewMessageError
    /**
     * Indicates that the parent message does not belong to the specified session (business logic validation).
     */
    data class InvalidParentInSession(val sessionId: Long, val parentId: Long) : ProcessNewMessageError
    /**
     * Indicates that a provided ID for a related entity (e.g., session, parent message)
     * does not exist in the database, detected via foreign key constraint.
     * Maps from MessageError.ForeignKeyViolation in the DAO layer.
     */
    data class InvalidRelatedEntity(val message: String) : ProcessNewMessageError
    /**
     * Indicates that the session's current model or settings are not configured correctly or not found.
     * Maps from ModelError.ModelNotFound, SettingsError.SettingsNotFound, or business logic check.
     */
    data class ModelConfigurationError(val message: String) : ProcessNewMessageError
    /**
     * Indicates a failure occurred when calling the external LLM API.
     * Wraps the external service error details.
     * Maps from exceptions or specific error responses from LLMApiClient.
     */
    data class ExternalServiceError(val message: String) : ProcessNewMessageError
}
```

**`message/UpdateMessageContentError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.message

/**
 * Represents possible errors when updating the content of a message.
 */
sealed interface UpdateMessageContentError {
    /**
     * Indicates that the message with the specified ID was not found.
     * Maps from MessageError.MessageNotFound in the DAO layer.
     */
    data class MessageNotFound(val id: Long) : UpdateMessageContentError
}
```

**`message/DeleteMessageError.kt`**
```kotlin
package eu.torvian.chatbot.server.service.error.message

/**
 * Represents possible errors when deleting a message.
 */
sealed interface DeleteMessageError {
    /**
     * Indicates that the message with the specified ID was not found.
     * Maps from MessageError.MessageNotFound in the DAO layer.
     */
    data class MessageNotFound(val id: Long) : DeleteMessageError
}
```

---

**2. Service Interface Files (with updated imports and return types)**

Update the existing service interface files:

**`server/src/main/kotlin/eu/torvian/chatbot/server/service/SessionService.kt`**
```kotlin
package eu.torvian.chatbot.server.service
import arrow.core.Either
import eu.torvian.chatbot.common.models.ChatSession
import eu.torvian.chatbot.common.models.ChatSessionSummary
import eu.torvian.chatbot.server.service.error.session.*


/**
 * Service interface for managing chat sessions.
 * Contains core business logic related to sessions, independent of API or data access details.
 */
interface SessionService {
    /**
     * Retrieves summaries for all chat sessions, including group names.
     * @return A list of [ChatSessionSummary] objects. Returns an empty list if no sessions exist.
     */
    suspend fun getAllSessionsSummaries(): List<ChatSessionSummary>
    /**
     * Creates a new chat session.
     * @param name Optional name for the session. If null or blank, a default name may be generated.
     * @return Either a [CreateSessionError] if the request is invalid or creation fails,
     *         or the newly created [ChatSession].
     */
    suspend fun createSession(name: String?): Either<CreateSessionError, ChatSession>
    /**
     * Retrieves full details for a specific chat session, including all messages.
     * @param id The ID of the session to retrieve.
     * @return Either a [GetSessionDetailsError] if the session doesn't exist,
     *         or the [ChatSession] object with messages.
     */
    suspend fun getSessionDetails(id: Long): Either<GetSessionDetailsError, ChatSession>
    
    /**
     * Updates the name of an existing chat session.
     * @param id The ID of the session to update.
     * @param name The new name for the session.
     * @return Either an [UpdateSessionNameError] or Unit if successful.
     */
    suspend fun updateSessionName(id: Long, name: String): Either<UpdateSessionNameError, Unit>

    /**
     * Updates the group ID of an existing chat session.
     * @param id The ID of the session to update.
     * @param groupId The new optional group ID for the session.
     * @return Either an [UpdateSessionGroupIdError] if the session or group is not found,
     *         or Unit if successful.
     */
    suspend fun updateSessionGroupId(id: Long, groupId: Long?): Either<UpdateSessionGroupIdError, Unit>

    /**
     * Updates the current model ID of an existing chat session.
     * @param id The ID of the session to update.
     * @param modelId The new optional model ID for the session.
     * @return Either an [UpdateSessionCurrentModelIdError] if the session or model is not found,
     *         or Unit if successful.
     */
    suspend fun updateSessionCurrentModelId(id: Long, modelId: Long?): Either<UpdateSessionCurrentModelIdError, Unit>

    /**
     * Updates the current settings ID of an existing chat session.
     * @param id The ID of the session to update.
     * @param settingsId The new optional settings ID for the session.
     * @return Either an [UpdateSessionCurrentSettingsIdError] if the session or settings are not found,
     *         or Unit if successful.
     */
    suspend fun updateSessionCurrentSettingsId(id: Long, settingsId: Long?): Either<UpdateSessionCurrentSettingsIdError, Unit>


    /**
     * Updates the current leaf message ID of an existing chat session.
     * @param id The ID of the session to update.
     * @param messageId The new optional leaf message ID for the session.
     * @return Either an [UpdateSessionLeafMessageIdError] if the session or message is not found,
     *         or Unit if successful.
     */
    suspend fun updateSessionLeafMessageId(id: Long, messageId: Long?): Either<UpdateSessionLeafMessageIdError, Unit>
    
    /**
     * Deletes a chat session and all its messages.
     * @param id The ID of the session to delete.
     * @return Either a [DeleteSessionError] if the session doesn't exist, or Unit if successful.
     */
    suspend fun deleteSession(id: Long): Either<DeleteSessionError, Unit>

}
```

**`server/src/main/kotlin/eu/torvian/chatbot/server/service/GroupService.kt`**
```kotlin
package eu.torvian.chatbot.server.service
import arrow.core.Either
import eu.torvian.chatbot.common.models.ChatGroup
import eu.torvian.chatbot.server.service.error.group.*

/**
 * Service interface for managing chat session groups.
 * Defines business logic for group creation, retrieval, renaming, and deletion.
 */
interface GroupService {
    /**
     * Retrieves a list of all chat groups.
     * @return A list of [ChatGroup] objects. Returns an empty list if no groups exist.
     */
    suspend fun getAllGroups(): List<ChatGroup>
    /**
     * Creates a new chat group.
     * @param name The name for the new group. Must not be blank.
     * @return Either a [CreateGroupError] if the name is invalid or creation fails,
     *         or the newly created [ChatGroup].
     */
    suspend fun createGroup(name: String): Either<CreateGroupError, ChatGroup>
    /**
     * Renames an existing chat group.
     * @param id The ID of the group to rename.
     * @param newName The new name for the group. Must not be blank.
     * @return Either a [RenameGroupError] if the group is not found or the new name is invalid,
     *         or Unit if successful.
     */
    suspend fun renameGroup(id: Long, newName: String): Either<RenameGroupError, Unit>
    /**
     * Deletes a chat group by ID.
     * Sessions previously assigned to this group will become ungrouped.
     * @param id The ID of the group to delete.
     * @return Either a [DeleteGroupError] if the group doesn't exist, or Unit if successful.
     */
    suspend fun deleteGroup(id: Long): Either<DeleteGroupError, Unit>
}
```

**`server/src/main/kotlin/eu/torvian/chatbot/server/service/ModelService.kt`**
```kotlin
package eu.torvian.chatbot.server.service
import arrow.core.Either
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.ModelSettings
import eu.torvian.chatbot.server.service.error.model.AddModelError
import eu.torvian.chatbot.server.service.error.model.UpdateModelError
import eu.torvian.chatbot.server.service.error.model.DeleteModelError
import eu.torvian.chatbot.server.service.error.settings.GetSettingsByIdError // Settings errors are related but in 'settings' package
import eu.torvian.chatbot.server.service.error.settings.AddSettingsError
import eu.torvian.chatbot.server.service.error.settings.UpdateSettingsError
import eu.torvian.chatbot.server.service.error.settings.DeleteSettingsError


/**
 * Service interface for managing LLM Models and their Settings.
 */
interface ModelService {
    /**
     * Retrieves all LLM model configurations.
     */
    suspend fun getAllModels(): List<LLMModel>
    /**
     * Retrieves a single LLM model by its unique identifier.
     *
     * @param id The unique identifier of the LLM model to retrieve.
     * @return [Either] a [GetModelError.ModelNotFound] if the model doesn't exist, or the [LLMModel].
     */
    suspend fun getModelById(id: Long): Either<GetModelError.ModelNotFound, LLMModel>
    /**
     * Adds a new LLM model configuration.
     * Handles secure storage of the API key if provided.
     * @param name The display name for the model.
     * @param baseUrl The base URL for the LLM API.
     * @param type The type of LLM provider.
     * @param apiKey Optional raw API key string to be stored securely.
     * @return Either an [AddModelError] if model creation or key storage fails,
     *         or the newly created [LLMModel] (without the raw key).
     */
    suspend fun addModel(name: String, baseUrl: String, type: String, apiKey: String?): Either<AddModelError, LLMModel>
    /**
     * Updates an existing LLM model configuration.
     * Handles updating the API key if a new one is provided.
     * @param id The ID of the model to update.
     * @param name New display name (optional).
     * @param baseUrl New base URL (optional).
     * @param type New type (optional).
     * @param apiKey Optional new raw API key string to update the stored key.
     * @return Either an [UpdateModelError] if the model is not found, update fails, or key update fails,
     *         or the updated [LLMModel] (without the raw key).
     */
    suspend fun updateModel(id: Long, name: String?, baseUrl: String?, type: String?, apiKey: String?): Either<UpdateModelError, LLMModel>
    /**
     * Deletes an LLM model configuration.
     * Handles deletion of associated settings and the securely stored API key.
     * @param id The ID of the model to delete.
     * @return Either a [DeleteModelError] if the model doesn't exist or deletion fails, or Unit if successful.
     */
    suspend fun deleteModel(id: Long): Either<DeleteModelError, Unit>
    /**
     * Retrieves a specific settings profile by ID.
     * @param id The ID of the settings profile.
     * @return Either a [GetSettingsByIdError] if not found, or the [ModelSettings].
     */
    suspend fun getSettingsById(id: Long): Either<GetSettingsByIdError, ModelSettings>
    /**
     * Retrieves all settings profiles stored in the database.
     * @return A list of all [ModelSettings] objects.
     */
    suspend fun getAllSettings(): List<ModelSettings>
    /**
     * Retrieves all settings profiles associated with a specific LLM model.
     * @param modelId The ID of the LLM model.
     * @return A list of [ModelSettings] for the model, or an empty list if none exist.
     */
    suspend fun getSettingsByModelId(modelId: Long): List<ModelSettings>
    /**
     * Creates a new settings profile with the specified parameters.
     *
     * @param name The display name of the settings profile (e.g., "Default", "Creative")
     * @param modelId The ID of the LLM model this settings profile is associated with
     * @param systemMessage Optional system message/prompt to include in the conversation context
     * @param temperature Optional sampling temperature for text generation
     * @param maxTokens Optional maximum number of tokens to generate in the response
     * @param customParamsJson Optional model-specific parameters stored as a JSON string
     * @return [Either] an [AddSettingsError] if the associated model doesn't exist or insertion fails, or the newly created [ModelSettings]
     */
    suspend fun addSettings(
        name: String,
        modelId: Long,
        systemMessage: String?,
        temperature: Float?,
        maxTokens: Int?,
        customParamsJson: String?
    ): Either<AddSettingsError, ModelSettings>
    /**
     * Updates an existing settings profile with new values.
     *
     * @param id The ID of the settings profile to update.
     * @param name New name (optional).
     * @param systemMessage New system message (optional).
     * @param temperature New temperature (optional).
     * @param maxTokens New max tokens (optional).
     * @param customParamsJson New custom params JSON (optional).
     * @return [Either] an [UpdateSettingsError] if not found or update fails, or [Unit] on success
     */
    suspend fun updateSettings(
        id: Long, name: String?, systemMessage: String?, temperature: Float?,
        maxTokens: Int?, customParamsJson: String?
    ): Either<UpdateSettingsError, Unit>
    /**
     * Deletes a settings profile with the specified ID.
     *
     * @param id The unique identifier of the settings profile to delete
     * @return [Either] a [DeleteSettingsError] if not found, or [Unit] on success
     */
    suspend fun deleteSettings(id: Long): Either<DeleteSettingsError, Unit>

    /**
     * Checks if an API key is configured for a specific model.
     * @param modelId The ID of the model.
     * @return True if an API key ID is stored for the model, false otherwise.
     */
    suspend fun isApiKeyConfiguredForModel(modelId: Long): Boolean // Does not return Either, simple boolean check

}
```

**`server/src/main/kotlin/eu/torvian/chatbot/server/service/MessageService.kt`**
```kotlin
package eu.torvian.chatbot.server.service
import arrow.core.Either
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.server.service.error.message.ProcessNewMessageError
import eu.torvian.chatbot.server.service.error.message.UpdateMessageContentError
import eu.torvian.chatbot.server.service.error.message.DeleteMessageError

/**
 * Service interface for managing Chat Messages and their threading relationships.
 * Contains core business logic for message processing and modification.
 */
interface MessageService {
    /**
     * Retrieves a list of all messages for a specific session, ordered by creation time.
     * @param sessionId The ID of the session.
     * @return A list of [ChatMessage] objects.
     */
    suspend fun getMessagesBySessionId(sessionId: Long): List<ChatMessage> // List doesn't use Either, assume session exists (checked by SessionService)
    /**
     * Processes a new incoming user message (including replies).
     *
     * Orchestrates saving the user message, building LLM context (thread-aware),
     * calling the LLM (stubbed in V1.1), saving the assistant message, and
     * updating thread relationships in the database.
     *
     * @param sessionId The ID of the session the message belongs to.
     * @param content The user's message content.
     * @param parentMessageId Optional ID of the message being replied to (null for root messages).
     * @return Either a [ProcessNewMessageError] if processing fails (e.g., session/parent not found, LLM config error, LLM API error),
     *         or a list containing the newly created user and assistant messages ([userMsg, assistantMsg]).
     */
    suspend fun processNewMessage(sessionId: Long, content: String, parentMessageId: Long? = null): Either<ProcessNewMessageError, List<ChatMessage>>
    /**
     * Updates the content of an existing message.
     * @param id The ID of the message to update.
     * @param content The new content.
     * @return Either an [UpdateMessageContentError] if the message doesn't exist,
     *         or the updated [ChatMessage].
     */
    suspend fun updateMessageContent(id: Long, content: String): Either<UpdateMessageContentError, ChatMessage>
    /**
     * Deletes a specific message and its children recursively.
     * Updates the parent's children list.
     * @param id The ID of the message to delete.
     * @return Either a [DeleteMessageError] if the message doesn't exist, or Unit if successful.
     */
    suspend fun deleteMessage(id: Long): Either<DeleteMessageError, Unit>
}
```

---

**3. Service Implementation Files (in `server/src/main/kotlin/eu/torvian/chatbot/server/service/impl/`)**

These files contain the concrete implementations of the Service interfaces, orchestrating calls to DAOs and other services.

**`SessionServiceImpl.kt`**
```kotlin
package eu.torvian.chatbot.server.service.impl
import arrow.core.*
import arrow.core.raise.*
import eu.torvian.chatbot.common.models.ChatSession
import eu.torvian.chatbot.common.models.ChatSessionSummary
import eu.torvian.chatbot.server.data.dao.SessionDao
import eu.torvian.chatbot.server.data.dao.error.SessionError
import eu.torvian.chatbot.server.service.SessionService
import eu.torvian.chatbot.server.service.error.session.*
import eu.torvian.chatbot.server.utils.transactions.TransactionScope

/**
 * Implementation of the [SessionService] interface.
 */
class SessionServiceImpl(
    private val sessionDao: SessionDao,
    private val transactionScope: TransactionScope,
) : SessionService {

    override suspend fun getAllSessionsSummaries(): List<ChatSessionSummary> {
        return transactionScope.transaction {
            sessionDao.getAllSessions()
        }
    }

    override suspend fun createSession(name: String?): Either<CreateSessionError, ChatSession> =
        transactionScope.transaction {
            either {
                if (name != null && name.isBlank()) {
                    raise(CreateSessionError.InvalidName("Session name cannot be blank."))
                }

                withError({ daoError: SessionError.ForeignKeyViolation ->
                    CreateSessionError.InvalidRelatedEntity(daoError.message)
                }) {
                    sessionDao.insertSession(name ?: "New Chat").bind()
                }
            }
        }

    override suspend fun getSessionDetails(id: Long): Either<GetSessionDetailsError, ChatSession> =
        transactionScope.transaction {
            either {
                withError({ daoError: SessionError.SessionNotFound ->
                    GetSessionDetailsError.SessionNotFound(daoError.id)
                }) {
                    sessionDao.getSessionById(id).bind()
                }
            }
        }

    override suspend fun updateSessionName(id: Long, name: String): Either<UpdateSessionNameError, Unit> =
        transactionScope.transaction {
            either {
                if (name.isBlank()) {
                    raise(UpdateSessionNameError.InvalidName("Session name cannot be blank."))
                }
                withError({ daoError: SessionError.SessionNotFound ->
                    UpdateSessionNameError.SessionNotFound(daoError.id)
                }) {
                    sessionDao.updateSessionName(id, name).bind()
                }
            }
        }

    override suspend fun updateSessionGroupId(id: Long, groupId: Long?): Either<UpdateSessionGroupIdError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: SessionError ->
                    when(daoError) {
                        is SessionError.SessionNotFound -> UpdateSessionGroupIdError.SessionNotFound(daoError.id)
                        is SessionError.ForeignKeyViolation -> UpdateSessionGroupIdError.InvalidRelatedEntity(daoError.message)
                    }
                }) {
                    sessionDao.updateSessionGroupId(id, groupId).bind()
                }
            }
        }

    override suspend fun updateSessionCurrentModelId(id: Long, modelId: Long?): Either<UpdateSessionCurrentModelIdError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: SessionError ->
                    when(daoError) {
                        is SessionError.SessionNotFound -> UpdateSessionCurrentModelIdError.SessionNotFound(daoError.id)
                        is SessionError.ForeignKeyViolation -> UpdateSessionCurrentModelIdError.InvalidRelatedEntity(daoError.message)
                    }
                }) {
                    sessionDao.updateSessionCurrentModelId(id, modelId).bind()
                }
            }
        }

    override suspend fun updateSessionCurrentSettingsId(id: Long, settingsId: Long?): Either<UpdateSessionCurrentSettingsIdError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: SessionError ->
                    when(daoError) {
                        is SessionError.SessionNotFound -> UpdateSessionCurrentSettingsIdError.SessionNotFound(daoError.id)
                        is SessionError.ForeignKeyViolation -> UpdateSessionCurrentSettingsIdError.InvalidRelatedEntity(daoError.message)
                    }
                }) {
                    sessionDao.updateSessionCurrentSettingsId(id, settingsId).bind()
                }
            }
        }

    override suspend fun updateSessionLeafMessageId(id: Long, messageId: Long?): Either<UpdateSessionLeafMessageIdError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: SessionError ->
                    when(daoError) {
                        is SessionError.SessionNotFound -> UpdateSessionLeafMessageIdError.SessionNotFound(daoError.id)
                        is SessionError.ForeignKeyViolation -> UpdateSessionLeafMessageIdError.InvalidRelatedEntity(daoError.message)
                    }
                }) {
                    sessionDao.updateSessionLeafMessageId(id, messageId).bind()
                }
            }
        }

    override suspend fun deleteSession(id: Long): Either<DeleteSessionError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: SessionError.SessionNotFound ->
                    DeleteSessionError.SessionNotFound(daoError.id)
                }) {
                    sessionDao.deleteSession(id).bind()
                }
            }
        }
}
```

**`GroupServiceImpl.kt`**
```kotlin
package eu.torvian.chatbot.server.service.impl
import arrow.core.*
import arrow.core.raise.*

import eu.torvian.chatbot.common.models.ChatGroup
import eu.torvian.chatbot.server.data.dao.GroupDao
import eu.torvian.chatbot.server.data.dao.SessionDao
import eu.torvian.chatbot.server.data.dao.error.GroupError
import eu.torvian.chatbot.server.service.GroupService
import eu.torvian.chatbot.server.service.error.group.*
import eu.torvian.chatbot.server.utils.transactions.TransactionScope

/**
 * Implementation of the [GroupService] interface.
 */
class GroupServiceImpl(
    private val groupDao: GroupDao,
    private val sessionDao: SessionDao,
    private val transactionScope: TransactionScope,
) : GroupService {

    override suspend fun getAllGroups(): List<ChatGroup> {
        return transactionScope.transaction {
            groupDao.getAllGroups()
        }
    }

    override suspend fun createGroup(name: String): Either<CreateGroupError, ChatGroup> =
        transactionScope.transaction {
            either {
                if (name.isBlank()) {
                    raise(CreateGroupError.InvalidName("Group name cannot be blank."))
                }
                groupDao.insertGroup(name)
            }
        }

    override suspend fun renameGroup(id: Long, newName: String): Either<RenameGroupError, Unit> =
        transactionScope.transaction {
            either {
                if (newName.isBlank()) {
                    raise(RenameGroupError.InvalidName("New group name cannot be blank."))
                }
                withError({ daoError: GroupError.GroupNotFound ->
                    RenameGroupError.GroupNotFound(daoError.id)
                }) {
                    groupDao.renameGroup(id, newName).bind()
                }
            }
        }

    override suspend fun deleteGroup(id: Long): Either<DeleteGroupError, Unit> =
        transactionScope.transaction {
            either {
                // If the group exists, ungroup sessions.
                sessionDao.ungroupSessions(id)
                
                withError({ daoError: GroupError.GroupNotFound ->
                    DeleteGroupError.GroupNotFound(daoError.id)
                }) {
                    groupDao.deleteGroup(id).bind()
                }
            }
        }
}
```

**`ModelServiceImpl.kt`**
```kotlin
package eu.torvian.chatbot.server.service.impl

import arrow.core.Either
import arrow.core.*
import arrow.core.raise.*
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.ModelSettings
import eu.torvian.chatbot.server.data.dao.ModelDao
import eu.torvian.chatbot.server.data.dao.SettingsDao
import eu.torvian.chatbot.server.data.dao.error.ModelError
import eu.torvian.chatbot.server.data.dao.error.SettingsError
import eu.torvian.chatbot.server.service.ModelService
import eu.torvian.chatbot.server.service.error.model.*
import eu.torvian.chatbot.server.service.error.settings.*
import eu.torvian.chatbot.server.service.security.CredentialManager
import eu.torvian.chatbot.server.service.security.error.CredentialError
import eu.torvian.chatbot.server.utils.transactions.TransactionScope

/**
 * Implementation of the [ModelService] interface.
 */
class ModelServiceImpl(
    private val modelDao: ModelDao,
    private val settingsDao: SettingsDao,
    private val credentialManager: CredentialManager,
    private val transactionScope: TransactionScope,
) : ModelService {

    override suspend fun getAllModels(): List<LLMModel> {
        return transactionScope.transaction {
            modelDao.getAllModels()
        }
    }

    override suspend fun getModelById(id: Long): Either<GetModelError.ModelNotFound, LLMModel> =
        transactionScope.transaction {
            either {
                withError({ daoError: ModelError.ModelNotFound ->
                    GetModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.getModelById(id).bind()
                }
            }
        }

    override suspend fun addModel(name: String, baseUrl: String, type: String, apiKey: String?): Either<AddModelError, LLMModel> =
        transactionScope.transaction {
            either {
                if (name.isBlank()) {
                    raise(AddModelError.InvalidInput("Model name cannot be blank."))
                }
                if (baseUrl.isBlank()) {
                    raise(AddModelError.InvalidInput("Model base URL cannot be blank."))
                }
                if (type.isBlank()) {
                    raise(AddModelError.InvalidInput("Model type cannot be blank."))
                }

                val apiKeyId: String? = if (!apiKey.isNullOrBlank()) {
                    credentialManager.storeCredential(apiKey)
                } else {
                    null
                }

                modelDao.insertModel(name, baseUrl, type, apiKeyId)
            }
        }

    override suspend fun updateModel(id: Long, name: String?, baseUrl: String?, type: String?, apiKey: String?): Either<UpdateModelError, LLMModel> =
        transactionScope.transaction {
            either {
                if (name != null && name.isBlank()) raise(UpdateModelError.InvalidInput("Name cannot be blank."))
                if (baseUrl != null && baseUrl.isBlank()) raise(UpdateModelError.InvalidInput("Base URL cannot be blank."))
                if (type != null && type.isBlank()) raise(UpdateModelError.InvalidInput("Type cannot be blank."))

                val existingModel = withError({ daoError: ModelError.ModelNotFound ->
                    UpdateModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.getModelById(id).bind()
                }

                var apiKeyIdToUpdate = existingModel.apiKeyId

                if (!apiKey.isNullOrBlank()) {
                    existingModel.apiKeyId?.let { oldKeyId ->
                        withError({ deleteErr: CredentialError.CredentialNotFound ->
                            UpdateModelError.CredentialError("Associated API key not found during deletion attempt for old key.")
                        }) {
                            credentialManager.deleteCredential(oldKeyId).bind()
                        }
                    }

                    apiKeyIdToUpdate = credentialManager.storeCredential(apiKey)
                }

                val updatedModel = existingModel.copy(
                    name = name ?: existingModel.name,
                    baseUrl = baseUrl ?: existingModel.baseUrl,
                    type = type ?: existingModel.type,
                    apiKeyId = apiKeyIdToUpdate
                )

                withError({ daoError: ModelError.ModelNotFound ->
                    UpdateModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.updateModel(updatedModel).bind()
                }

                updatedModel
            }
        }

    override suspend fun deleteModel(id: Long): Either<DeleteModelError, Unit> =
        transactionScope.transaction {
            either {
                val modelToDelete = withError({ daoError: ModelError.ModelNotFound ->
                    DeleteModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.getModelById(id).bind()
                }

                modelToDelete.apiKeyId?.let { keyId ->
                    withError({ deleteErr: CredentialError.CredentialNotFound ->
                        DeleteModelError.CredentialDeletionError("Associated API key not found during deletion attempt.")
                    }) {
                        credentialManager.deleteCredential(keyId).bind()
                    }
                }

                withError({ daoError: ModelError.ModelNotFound ->
                    DeleteModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.deleteModel(id).bind()
                }
            }
        }

    override suspend fun getSettingsById(id: Long): Either<GetSettingsByIdError, ModelSettings> =
        transactionScope.transaction {
            either {
                withError({ daoError: SettingsError.SettingsNotFound ->
                    GetSettingsByIdError.SettingsNotFound(daoError.id)
                }) {
                    settingsDao.getSettingsById(id).bind()
                }
            }
        }

    override suspend fun getAllSettings(): List<ModelSettings> {
        return transactionScope.transaction {
            settingsDao.getAllSettings()
        }
    }

    override suspend fun getSettingsByModelId(modelId: Long): List<ModelSettings> {
        return transactionScope.transaction {
            settingsDao.getSettingsByModelId(modelId)
        }
    }

    override suspend fun addSettings(
        name: String, modelId: Long, systemMessage: String?,
        temperature: Float?, maxTokens: Int?, customParamsJson: String?
    ): Either<AddSettingsError, ModelSettings> =
        transactionScope.transaction {
            either {
                if (name.isBlank()) {
                    raise(AddSettingsError.InvalidInput("Settings name cannot be blank."))
                }
                if (temperature != null && (temperature < 0f || temperature > 2f)) {
                    raise(AddSettingsError.InvalidInput("Temperature must be between 0.0 and 2.0"))
                }
                if (maxTokens != null && maxTokens <= 0) {
                    raise(AddSettingsError.InvalidInput("Max tokens must be positive"))
                }

                withError({ daoError: SettingsError.ModelNotFound ->
                    AddSettingsError.ModelNotFound(daoError.modelId)
                }) {
                    settingsDao.insertSettings(name, modelId, systemMessage, temperature, maxTokens, customParamsJson).bind()
                }
            }
        }

    override suspend fun updateSettings(
        id: Long, name: String?, systemMessage: String?, temperature: Float?,
        maxTokens: Int?, customParamsJson: String?
    ): Either<UpdateSettingsError, Unit> =
        transactionScope.transaction {
            either {
                if (name != null && name.isBlank()) {
                    raise(UpdateSettingsError.InvalidInput("Settings name cannot be blank."))
                }
                if (temperature != null && (temperature < 0f || temperature > 2f)) {
                    raise(UpdateSettingsError.InvalidInput("Temperature must be between 0.0 and 2.0"))
                }
                if (maxTokens != null && maxTokens <= 0) {
                    raise(UpdateSettingsError.InvalidInput("Max tokens must be positive"))
                }

                val existingSettings = withError({ daoError: SettingsError.SettingsNotFound ->
                    UpdateSettingsError.SettingsNotFound(daoError.id)
                }) {
                    settingsDao.getSettingsById(id).bind()
                }

                val updatedSettings = existingSettings.copy(
                    name = name ?: existingSettings.name,
                    systemMessage = systemMessage ?: existingSettings.systemMessage,
                    temperature = temperature ?: existingSettings.temperature,
                    maxTokens = maxTokens ?: existingSettings.maxTokens,
                    customParamsJson = customParamsJson ?: existingSettings.customParamsJson
                )
                withError({ daoError: SettingsError ->
                    when(daoError) {
                        is SettingsError.SettingsNotFound -> UpdateSettingsError.SettingsNotFound(daoError.id)
                        is SettingsError.ModelNotFound -> UpdateSettingsError.InvalidInput("Model associated with settings not found during update")
                    }
                }) {
                    settingsDao.updateSettings(updatedSettings).bind()
                }
            }
        }

    override suspend fun deleteSettings(id: Long): Either<DeleteSettingsError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: SettingsError.SettingsNotFound ->
                    DeleteSettingsError.SettingsNotFound(daoError.id)
                }) {
                    settingsDao.deleteSettings(id).bind()
                }
            }
        }

    override suspend fun isApiKeyConfiguredForModel(modelId: Long): Boolean {
        return transactionScope.transaction {
            modelDao.getModelById(modelId)
                .map { it.apiKeyId != null }
                .getOrElse { false }
        }
    }
}
```

**`MessageServiceImpl.kt`**
```kotlin
package eu.torvian.chatbot.server.service.impl
import arrow.core.*
import arrow.core.raise.*

import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.ModelSettings
import eu.torvian.chatbot.server.data.dao.MessageDao
import eu.torvian.chatbot.server.data.dao.SessionDao
import eu.torvian.chatbot.server.data.dao.error.MessageError
import eu.torvian.chatbot.server.data.dao.error.MessageAddChildError
import eu.torvian.chatbot.server.data.dao.error.MessageRemoveChildError
import eu.torvian.chatbot.server.data.dao.error.SessionError
import eu.torvian.chatbot.server.data.dao.error.ModelError
import eu.torvian.chatbot.server.data.dao.error.SettingsError

import eu.torvian.chatbot.server.external.llm.LLMApiClient
import eu.torvian.chatbot.server.external.models.OpenAiApiModels.ChatCompletionResponse // Needed for LLM response type
import eu.torvian.chatbot.server.service.MessageService
import eu.torvian.chatbot.server.service.ModelService // Needed to get Model/Settings via service? Or use DAOs directly?
import eu.torvian.chatbot.server.service.error.message.ProcessNewMessageError
import eu.torvian.chatbot.server.service.error.message.UpdateMessageContentError
import eu.torvian.chatbot.server.service.error.message.DeleteMessageError
import eu.torvian.chatbot.server.service.error.model.GetSettingsByIdError // Need this if using ModelService::getSettingsById

import eu.torvian.chatbot.server.utils.transactions.TransactionScope

/**
 * Implementation of the [MessageService] interface.
 * Handles the business logic for processing and managing chat messages.
 * Uses a [TransactionScope] and Arrow Raise DSL for atomic operations and error handling.
 */
class MessageServiceImpl(
    private val messageDao: MessageDao,
    private val sessionDao: SessionDao,
    private val modelService: ModelService,
    private val llmApiClient: LLMApiClient,
    private val credentialManager: CredentialManager,
    private val transactionScope: TransactionScope,
) : MessageService {

    override suspend fun getMessagesBySessionId(sessionId: Long): List<ChatMessage> {
        return transactionScope.transaction {
            messageDao.getMessagesBySessionId(sessionId)
        }
    }

    override suspend fun processNewMessage(sessionId: Long, content: String, parentMessageId: Long?): Either<ProcessNewMessageError, List<ChatMessage>> =
        transactionScope.transaction {
            either {

                // 1. Validate session
                val session = withError({ daoError: SessionError.SessionNotFound ->
                    ProcessNewMessageError.SessionNotFound(daoError.id)
                }) {
                    sessionDao.getSessionById(sessionId).bind()
                }

                // 2. Validate parent message (if any)
                if (parentMessageId != null) {
                    withError({ daoError: MessageError.MessageNotFound ->
                        ProcessNewMessageError.ParentMessageNotFound(daoError.id)
                    }) {
                        messageDao.getMessageById(parentMessageId).bind()
                    }.also { parentMessage ->
                        if (parentMessage.sessionId != sessionId) {
                            raise(ProcessNewMessageError.InvalidParentInSession(sessionId, parentMessageId))
                        }
                    }
                }

                // 3. Save user message
                val userMessage = withError({ daoError: MessageError.ForeignKeyViolation ->
                    ProcessNewMessageError.InvalidRelatedEntity("Failed to link user message: ${daoError.message}")
                }) {
                    messageDao.insertUserMessage(sessionId, content, parentMessageId).bind()
                }

                // 4. Update parent's children list (if any)
                if (parentMessageId != null) {
                    withError({ daoError: MessageAddChildError ->
                        when(daoError) {
                            is MessageAddChildError.ParentNotFound -> ProcessNewMessageError.ParentMessageNotFound(daoError.parentId)
                            is MessageAddChildError.ChildAlreadyExists -> ProcessNewMessageError.InvalidInput("Child message ${daoError.childId} already exists for parent ${daoError.parentId}")
                        }
                    }) {
                        messageDao.addChildToMessage(parentMessageId, userMessage.id).bind() // .bind() handles Either<MessageAddChildError, Unit>
                    }
                }

                // 5. Get model and settings config
                val modelId = session.currentModelId ?: raise(ProcessNewMessageError.ModelConfigurationError("No model selected for session"))
                val settingsId = session.currentSettingsId ?: raise(ProcessNewMessageError.ModelConfigurationError("No settings selected for session"))

                // Fetch Model
                val model = withError({ serviceError: GetModelError.ModelNotFound ->
                    ProcessNewMessageError.ModelConfigurationError("Model with ID ${serviceError.id} not found")
                }) {
                    modelService.getModelById(modelId).bind()
                }

                // Fetch Settings
                val settings = withError({ serviceError: GetSettingsByIdError ->
                    when(serviceError) {
                        is GetSettingsByIdError.SettingsNotFound -> ProcessNewMessageError.ModelConfigurationError("Settings with ID ${serviceError.id} not found")
                    }
                }) {
                    modelService.getSettingsById(settingsId).bind()
                }

                // Get API Key
                val apiKey: String? = model.apiKeyId?.let { keyId ->
                    withError({ credError: CredentialError.CredentialNotFound ->
                        ProcessNewMessageError.ModelConfigurationError("API key not found in secure storage for model ID ${model.id} (key alias: $keyId)")
                    }) {
                        credentialManager.getCredential(keyId).bind()
                    }
                }
                
                // 6. Build context
                val allMessagesInSession = messageDao.getMessagesBySessionId(sessionId) // Returns List, no Either, no bind/withError needed here
                val context = buildContext(userMessage, allMessagesInSession)
                
                // 7. Call LLM API (stubbed for S1)
                val llmResponse = withError({ externalErrorMsg: String ->
                    ProcessNewMessageError.ExternalServiceError("LLM API Error: $externalErrorMsg")
                }) {
                    llmApiClient.completeChat(
                        messages = context,
                        modelConfig = model,
                        settings = settings,
                        apiKey = apiKey
                    ).bind()
                }

                // 8. Save assistant message
                val assistantMessageContent = llmResponse.choices.firstOrNull()?.message?.content ?: "Error: Empty LLM response"
                val assistantMessage = withError({ daoError: MessageError.ForeignKeyViolation ->
                    ProcessNewMessageError.InvalidRelatedEntity("Failed to link assistant message: ${daoError.message}")
                }) {
                    messageDao.insertAssistantMessage(
                        sessionId,
                        assistantMessageContent,
                        userMessage.id,
                        modelId,
                        settingsId
                    ).bind()
                }

                // 9. Update user message's children list
                withError({ daoError: MessageAddChildError ->
                    when(daoError) {
                        is MessageAddChildError.ParentNotFound -> ProcessNewMessageError.ParentMessageNotFound(daoError.parentId)
                        is MessageAddChildError.ChildAlreadyExists -> ProcessNewMessageError.InvalidInput("Child message ${daoError.childId} already exists for parent ${daoError.parentId}")
                    }
                }) {
                    messageDao.addChildToMessage(userMessage.id, assistantMessage.id).bind()
                }

                // 10. Update session's leaf message ID
                withError({ daoError: SessionError ->
                    when(daoError) {
                        is SessionError.SessionNotFound -> ProcessNewMessageError.SessionNotFound(daoError.id)
                        is SessionError.ForeignKeyViolation -> ProcessNewMessageError.InvalidInput("Failed to update session leaf message ID: ${daoError.message}") // Foreign key to Message table
                    }
                }) {
                    sessionDao.updateSessionLeafMessageId(sessionId, assistantMessage.id).bind() // .bind() handles Either<SessionError, Unit>
                }

                // 11. Return new messages as the success value
                listOf(userMessage, assistantMessage)
            }
        }

    private fun buildContext(currentUserMessage: ChatMessage, allMessages: List<ChatMessage>): List<ChatMessage> {
        // Simplified context building for Sprint 1 stub - keeps thread branch
        val context = mutableListOf<ChatMessage>()
        val messageMap = allMessages.associateBy { it.id }
        var c: ChatMessage? = currentUserMessage
        while (c != null) {
            context.add(0, c)
            c = c.parentMessageId?.let { messageMap[it] }
        }
        return context
    }

    override suspend fun updateMessageContent(id: Long, content: String): Either<UpdateMessageContentError, ChatMessage> =
        transactionScope.transaction {
            either {
                withError({ daoError: MessageError.MessageNotFound ->
                    UpdateMessageContentError.MessageNotFound(daoError.id)
                }) {
                    messageDao.updateMessageContent(id, content).bind()
                }
            }
        }

    override suspend fun deleteMessage(id: Long): Either<DeleteMessageError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: MessageError.MessageNotFound ->
                    DeleteMessageError.MessageNotFound(daoError.id)
                }) {
                    messageDao.deleteMessage(id).bind()
                }
            }
        }
}
```

---

**4. External Service Stubs (`server` module)**

For Sprint 1, the `LLMApiClient` is stubbed to provide fake data, allowing the service layer to be fully implemented and tested without a live external dependency.

**`LLMApiClient.kt`** (Interface remains the same, included for completeness)
```kotlin
package eu.torvian.chatbot.server.external.llm

import arrow.core.Either
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.ModelSettings
import eu.torvian.chatbot.server.external.models.OpenAiApiModels.ChatCompletionResponse

/**
 * Interface for interacting with external LLM APIs (OpenAI-compatible).
 */
interface LLMApiClient {
    /**
     * Sends a chat completion request to the LLM API.
     * @param messages The list of messages forming the conversation context.
     * @param modelConfig Configuration details for the target LLM endpoint.
     * @param settings Specific settings profile to use for this completion request.
     * @param apiKey The decrypted API key for authentication (nullable if not required).
     * @return Either an error string or the response from the LLM API.
     */
    suspend fun completeChat(
        messages: List<ChatMessage>,
        modelConfig: LLMModel,
        settings: ModelSettings,
        apiKey: String?
    ): Either<String, ChatCompletionResponse>
}
```

**`LLMApiClientKtor.kt`**
```kotlin
package eu.torvian.chatbot.server.external.llm

import arrow.core.Either
import arrow.core.right
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.ModelSettings
import eu.torvian.chatbot.server.external.models.OpenAiApiModels.ChatCompletionResponse
import io.ktor.client.HttpClient
import kotlinx.coroutines.delay
import java.util.UUID

/**
 * Ktor implementation of the [LLMApiClient] for OpenAI-compatible endpoints.
 *
 * NOTE: For Sprint 1, this implementation is STUBBED to return a canned response
 * without making an actual network call.
 */
class LLMApiClientKtor(private val httpClient: HttpClient) : LLMApiClient {

    override suspend fun completeChat(
        messages: List<ChatMessage>,
        modelConfig: LLMModel,
        settings: ModelSettings,
        apiKey: String?
    ): Either<String, ChatCompletionResponse> {
        delay(800) // Simulate network latency

        val fakeContent = "This is a stubbed response for model '${modelConfig.name}' " +
                          "replying to: \"${messages.lastOrNull()?.content?.take(40)}...\""

        val choice = ChatCompletionResponse.Choice(
            index = 0,
            message = ChatCompletionResponse.Choice.Message("assistant", fakeContent),
            finish_reason = "stop"
        )

        return ChatCompletionResponse(
            id = "stub-${UUID.randomUUID()}",
            `object` = "chat.completion",
            created = System.currentTimeMillis() / 1000,
            model = modelConfig.name,
            choices = listOf(choice),
            usage = ChatCompletionResponse.Usage(10, 10, 20)
        ).right()

        // Real implementation for Sprint 2+ would go here...
    }
}
```

---

**5. Add New Request Body Files (in `common/src/main/kotlin/eu/torvian/chatbot/common/models/`)**

**`UpdateSessionNameRequest.kt`**
```kotlin
package eu.torvian.chatbot.common.models
import kotlinx.serialization.Serializable
/**
 * Request body for updating the name of a chat session.
 *
 * @property name The new name for the session.
 */
@Serializable
data class UpdateSessionNameRequest(
    val name: String
)
```

**`UpdateSessionGroupRequest.kt`**
```kotlin
package eu.torvian.chatbot.common.models
import kotlinx.serialization.Serializable
/**
 * Request body for updating the group ID of a chat session.
 *
 * @property groupId The new optional group ID for the session. Null means ungrouped.
 */
@Serializable
data class UpdateSessionGroupRequest(
    val groupId: Long?
)
```

**`UpdateSessionModelRequest.kt`**
```kotlin
package eu.torvian.chatbot.common.models
import kotlinx.serialization.Serializable
/**
 * Request body for updating the current model ID of a chat session.
 *
 * @property modelId The new optional model ID for the session. Null means no model selected.
 */
@Serializable
data class UpdateSessionModelRequest(
    val modelId: Long?
)
```

**`UpdateSessionSettingsRequest.kt`**
```kotlin
package eu.torvian.chatbot.common.models
import kotlinx.serialization.Serializable
/**
 * Request body for updating the current settings ID of a chat session.
 *
 * @property settingsId The new optional settings ID for the session. Null means no settings selected.
 */
@Serializable
data class UpdateSessionSettingsRequest(
    val settingsId: Long?
)
```

**`UpdateSessionLeafMessageRequest.kt`**
```kotlin
package eu.torvian.chatbot.common.models
import kotlinx.serialization.Serializable
/**
 * Request body for updating the current leaf message ID of a chat session.
 *
 * @property leafMessageId The new optional leaf message ID for the session. Null means no leaf message (session has no messages).
 */
@Serializable
data class UpdateSessionLeafMessageRequest(
    val leafMessageId: Long?
)
```

**6. Ktor Routing (`server/src/main/kotlin/eu/torvian/chatbot/server/api/server/ApiRoutes.kt`)**

This file wires the Ktor server endpoints to the Service layer methods, handling HTTP requests and formatting responses, including mapping service errors to HTTP status codes.

```kotlin
// file: server/src/main/kotlin/eu/torvian/chatbot/server/api/server/ApiRoutes.kt
package eu.torvian.chatbot.server.api.server

import arrow.core.Either
import eu.torvian.chatbot.common.models.*
import eu.torvian.chatbot.server.service.GroupService
import eu.torvian.chatbot.server.service.MessageService
import eu.torvian.chatbot.server.service.ModelService
import eu.torvian.chatbot.server.service.SessionService
import eu.torvian.chatbot.server.service.error.model.*
import eu.torvian.chatbot.server.service.error.settings.*
import eu.torvian.chatbot.server.service.error.session.*

import io.ktor.http.HttpStatusCode
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*

/**
 * Extension function to respond with the result of an Either, mapping Left to an error and Right to success.
 */
private suspend inline fun <reified R : Any, reified L : Any> ApplicationCall.respondEither(
    either: Either<L, R>,
    successCode: HttpStatusCode = HttpStatusCode.OK,
    noinline errorMapping: (L) -> Pair<HttpStatusCode, String> = { HttpStatusCode.InternalServerError to it.toString() }
) {
    when (either) {
        is Either.Right -> respond(successCode, either.value)
        is Either.Left -> {
            val (status, message) = errorMapping(either.value)
            // Log the error on the server side for debugging
            // application.environment.log.error("API Error: $message (Status: $status, Details: $either)")
            respond(status, message)
        }
    }
}

/**
 * Configures the Ktor server routing for the application API (v1).
 *
 * @param sessionService The injected SessionService instance.
 * @param groupService The injected GroupService instance.
 * @param modelService The injected ModelService instance.
 * @param messageService The injected MessageService instance.
 */
fun Application.configureRouting(
    sessionService: SessionService,
    groupService: GroupService,
    modelService: ModelService,
    messageService: MessageService,
) {
    routing {
        route("/api/v1") {
            // --- Session Routes ---
            route("/sessions") {
                get {
                    call.respond(sessionService.getAllSessionsSummaries())
                }
                post {
                    val request = call.receive<CreateSessionRequest>()
                    call.respondEither(sessionService.createSession(request.name), HttpStatusCode.Created) {
                        when (it) {
                            is CreateSessionError.InvalidName -> HttpStatusCode.BadRequest to it.reason
                            is CreateSessionError.InvalidRelatedEntity -> HttpStatusCode.BadRequest to it.message // Assuming client sends invalid FK ID
                        }
                    }
                }
                route("/{sessionId}") {
                    get {
                        val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@get call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                        call.respondEither(sessionService.getSessionDetails(sessionId)) {
                            when (it) {
                                is GetSessionDetailsError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.id}"
                            }
                        }
                    }

                    // Granular PUT routes for specific updates
                    put("/name") {
                         val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                         val request = call.receive<UpdateSessionNameRequest>()
                         call.respondEither(sessionService.updateSessionName(sessionId, request.name), HttpStatusCode.OK) {
                             when(it) {
                                 is UpdateSessionNameError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.id}"
                                 is UpdateSessionNameError.InvalidName -> HttpStatusCode.BadRequest to it.reason
                             }
                         }
                    }
                    put("/model") {
                        val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                        val request = call.receive<UpdateSessionModelRequest>()
                        call.respondEither(sessionService.updateSessionCurrentModelId(sessionId, request.modelId), HttpStatusCode.OK) {
                             when(it) {
                                 is UpdateSessionCurrentModelIdError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.id}"
                                 is UpdateSessionCurrentModelIdError.InvalidRelatedEntity -> HttpStatusCode.BadRequest to "Invalid model ID provided: ${request.modelId}"
                             }
                        }
                    }
                     put("/settings") {
                        val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                        val request = call.receive<UpdateSessionSettingsRequest>()
                        call.respondEither(sessionService.updateSessionCurrentSettingsId(sessionId, request.settingsId), HttpStatusCode.OK) {
                             when(it) {
                                 is UpdateSessionCurrentSettingsIdError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.id}"
                                 is UpdateSessionCurrentSettingsIdError.InvalidRelatedEntity -> HttpStatusCode.BadRequest to "Invalid settings ID provided: ${request.settingsId}"
                             }
                        }
                    }
                    put("/leafMessage") {
                         val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                         val request = call.receive<UpdateSessionLeafMessageRequest>()
                         call.respondEither(sessionService.updateSessionLeafMessageId(sessionId, request.leafMessageId), HttpStatusCode.OK) {
                             when(it) {
                                 is UpdateSessionLeafMessageIdError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.id}"
                                 is UpdateSessionLeafMessageIdError.InvalidRelatedEntity -> HttpStatusCode.BadRequest to "Invalid leaf message ID provided: ${request.leafMessageId}"
                             }
                         }
                    }

                    // Specific endpoint for assigning/ungrouping
                    put("/group") {
                        val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                        val request = call.receive<UpdateSessionGroupRequest>()
                        
                        call.respondEither(sessionService.updateSessionGroupId(sessionId, request.groupId), HttpStatusCode.OK) {
                             when(it) {
                                 is UpdateSessionGroupIdError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.id}"
                                 is UpdateSessionGroupIdError.InvalidRelatedEntity -> HttpStatusCode.BadRequest to "Invalid group ID provided: ${request.groupId}"
                             }
                        }
                    }

                    delete {
                        val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@delete call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                        call.respondEither(sessionService.deleteSession(sessionId), HttpStatusCode.NoContent) {
                            when(it) {
                                is DeleteSessionError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.id}"
                            }
                        }
                    }
                } // End sessions/{sessionId}
            } // End /sessions

            // --- Group Routes ---
            route("/groups") {
                get {
                    call.respond(groupService.getAllGroups())
                }
                post {
                    val request = call.receive<CreateGroupRequest>()
                    call.respondEither(groupService.createGroup(request.name), HttpStatusCode.Created) {
                         when(it) {
                            is CreateGroupError.InvalidName -> HttpStatusCode.BadRequest to it.reason
                            // Add mapping for AlreadyExists if implemented later
                         }
                    }
                }
                route("/{groupId}") {
                    delete {
                        val groupId = call.parameters["groupId"]?.toLongOrNull() ?: return@delete call.respond(HttpStatusCode.BadRequest, "Invalid group ID")
                        call.respondEither(groupService.deleteGroup(groupId), HttpStatusCode.NoContent) {
                            when(it) {
                                is DeleteGroupError.GroupNotFound -> HttpStatusCode.NotFound to "Group not found: ${it.id}"
                            }
                        }
                    }
                    put {
                        val groupId = call.parameters["groupId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid group ID")
                        val request = call.receive<RenameGroupRequest>()
                        call.respondEither(groupService.renameGroup(groupId, request.name), HttpStatusCode.OK) {
                             when(it) {
                                 is RenameGroupError.GroupNotFound -> HttpStatusCode.NotFound to "Group not found: ${it.id}"
                                 is RenameGroupError.InvalidName -> HttpStatusCode.BadRequest to it.reason
                             }
                        }
                    }
                    
                }
            }

            // --- Model Routes ---
            route("/models") {
                 get { // List all models
                     call.respond(modelService.getAllModels())
                 }
                 post { // Add new model
                     val request = call.receive<AddModelRequest>()
                     call.respondEither(modelService.addModel(request.name, request.baseUrl, request.type, request.apiKey), HttpStatusCode.Created) {
                         when(it) {
                             is AddModelError.InvalidInput -> HttpStatusCode.BadRequest to it.reason
                         }
                     }
                 }
                 route("/{modelId}") {
                     get { // Get model by ID
                         val modelId = call.parameters["modelId"]?.toLongOrNull() ?: return@get call.respond(HttpStatusCode.BadRequest,"Invalid model ID")
                         call.respondEither(modelService.getModelById(modelId)) {
                             when(it) {
                                 is GetModelError.ModelNotFound -> HttpStatusCode.NotFound to "Model not found: ${it.id}"
                             }
                         }
                     }
                     put { // Update model by ID
                         val modelId = call.parameters["modelId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest,"Invalid model ID")
                         val request = call.receive<UpdateModelRequest>()
                         // Note: Ignoring request.id and using path modelId
                         call.respondEither(modelService.updateModel(modelId, request.name, request.baseUrl, request.type, request.apiKey)) {
                             when(it) {
                                 is UpdateModelError.ModelNotFound -> HttpStatusCode.NotFound to "Model not found: ${it.id}"
                                 is UpdateModelError.InvalidInput -> HttpStatusCode.BadRequest to it.reason
                                 is UpdateModelError.CredentialError -> HttpStatusCode.InternalServerError to "Credential error: ${it.message}"
                                 // Add other update errors
                             }
                         }
                     }
                     delete { // Delete model by ID
                         val modelId = call.parameters["modelId"]?.toLongOrNull() ?: return@delete call.respond(HttpStatusCode.BadRequest,"Invalid model ID")
                         call.respondEither(modelService.deleteModel(modelId), HttpStatusCode.NoContent) {
                             when(it) {
                                 is DeleteModelError.ModelNotFound -> HttpStatusCode.NotFound to "Model not found: ${it.id}"
                                 is DeleteModelError.CredentialDeletionError -> HttpStatusCode.InternalServerError to "Credential deletion error: ${it.message}"
                                 // Add ModelInUse if implemented
                             }
                         }
                     }

                     // Nested settings routes under model
                     route("/settings") {
                         get { // List settings for this model
                             val modelId = call.parameters["modelId"]?.toLongOrNull() ?: return@get call.respond(HttpStatusCode.BadRequest,"Invalid model ID")
                             call.respond(modelService.getSettingsByModelId(modelId)) // Service doesn't use Either for this List retrieval
                         }
                         post { // Add new settings for this model
                             val modelId = call.parameters["modelId"]?.toLongOrNull() ?: return@post call.respond(HttpStatusCode.BadRequest,"Invalid model ID")
                             val request = call.receive<AddModelSettingsRequest>()
                             call.respondEither(
                                 modelService.addSettings(
                                     request.name,
                                     modelId,
                                     request.systemMessage,
                                     request.temperature,
                                     request.maxTokens,
                                     request.customParamsJson
                                 ), HttpStatusCode.Created
                             ) {
                                 when(it) {
                                     is AddSettingsError.ModelNotFound -> HttpStatusCode.BadRequest to "Model not found for settings: ${it.modelId}" // Mapped from FK violation
                                     is AddSettingsError.InvalidInput -> HttpStatusCode.BadRequest to it.reason
                                 }
                             }
                         }
                     }

                     // API Key Status for a model (existing route)
                     route("/apikey/status") {
                         get {
                             val modelId = call.parameters["modelId"]?.toLongOrNull() ?: return@get call.respond(HttpStatusCode.BadRequest,"Invalid model ID")
                             val isConfigured = modelService.isApiKeyConfiguredForModel(modelId)
                             call.respond(ApiKeyStatusResponse(isConfigured))
                         }
                     }
                 } // End /models/{modelId}
             } // End /models

            // --- Settings Routes (top-level for getting/updating/deleting specific settings) ---
            route("/settings") {
                // Note: Listing all settings is done via GET /models/{modelId}/settings or potentially a separate GET /settings route if needed

                route("/{settingsId}") {
                    get { // Get settings by ID
                        val settingsId = call.parameters["settingsId"]?.toLongOrNull() ?: return@get call.respond(HttpStatusCode.BadRequest,"Invalid settings ID")
                        call.respondEither(modelService.getSettingsById(settingsId)) {
                            when(it) {
                                is GetSettingsByIdError.SettingsNotFound -> HttpStatusCode.NotFound to "Settings not found: ${it.id}"
                            }
                        }
                    }
                    put { // Update settings by ID
                        val settingsId = call.parameters["settingsId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest,"Invalid settings ID")
                        val request = call.receive<UpdateSettingsRequest>()
                        // Note: Ignoring request.id and using path settingsId
                        call.respondEither(
                            modelService.updateSettings(
                                settingsId,
                                request.name,
                                request.systemMessage,
                                request.temperature,
                                request.maxTokens,
                                request.customParamsJson
                            )
                        ) {
                            when(it) {
                                is UpdateSettingsError.SettingsNotFound -> HttpStatusCode.NotFound to "Settings not found: ${it.id}"
                                is UpdateSettingsError.InvalidInput -> HttpStatusCode.BadRequest to it.reason
                                // Add ModelNotFound error if updateSettings could return it (e.g., if updating modelId was allowed)
                            }
                        }
                    }
                    delete { // Delete settings by ID
                        val settingsId = call.parameters["settingsId"]?.toLongOrNull() ?: return@delete call.respond(HttpStatusCode.BadRequest,"Invalid settings ID")
                        call.respondEither(modelService.deleteSettings(settingsId), HttpStatusCode.NoContent) {
                            when(it) {
                                is DeleteSettingsError.SettingsNotFound -> HttpStatusCode.NotFound to "Settings not found: ${it.id}"
                                // Add SettingsInUse if implemented
                            }
                        }
                    }
                } // End /settings/{settingsId}
            } // End /settings


            // --- Message Routes ---
             route("/sessions/{sessionId}/messages") {
                 post { // Process a new message
                     val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@post call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                     val request = call.receive<ProcessNewMessageRequest>()
                     call.respondEither(messageService.processNewMessage(sessionId, request.content, request.parentMessageId), HttpStatusCode.Created) {
                         when(it) {
                             is ProcessNewMessageError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.sessionId}"
                             is ProcessNewMessageError.ParentMessageNotFound -> HttpStatusCode.BadRequest to "Parent message not found: ${it.parentId}"
                             is ProcessNewMessageError.InvalidParentInSession -> HttpStatusCode.BadRequest to "Parent message does not belong to this session (parent: ${it.parentId}, session: ${it.sessionId})"
                             is ProcessNewMessageError.InvalidRelatedEntity -> HttpStatusCode.BadRequest to it.message
                             is ProcessNewMessageError.ModelConfigurationError -> HttpStatusCode.BadRequest to "LLM configuration error: ${it.message}"
                             is ProcessNewMessageError.ExternalServiceError -> HttpStatusCode.InternalServerError to "LLM API Error: ${it.message}"
                         }
                     }
                 }
             }
             route("/messages/{messageId}") {
                 put("/content") {
                     val messageId = call.parameters["messageId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid message ID")
                     val request = call.receive<UpdateMessageRequest>()
                     call.respondEither(messageService.updateMessageContent(messageId, request.content), HttpStatusCode.OK) {
                         when(it) {
                             is UpdateMessageContentError.MessageNotFound -> HttpStatusCode.NotFound to "Message not found: ${it.id}"
                         }
                     }
                 }
                 delete {
                     val messageId = call.parameters["messageId"]?.toLongOrNull() ?: return@delete call.respond(HttpStatusCode.BadRequest, "Invalid message ID")
                     call.respondEither(messageService.deleteMessage(messageId), HttpStatusCode.NoContent) {
                         when(it) {
                             is DeleteMessageError.MessageNotFound -> HttpStatusCode.NotFound to "Message not found: ${it.id}" 
                         }
                     }
                 }
             }

        } // End /api/v1
    }
}
```

This concludes the code for PR 5. This PR establishes the core business logic and API endpoints for many of the basic features, providing a solid foundation for the frontend to build upon, with clear error handling and atomic database operations.