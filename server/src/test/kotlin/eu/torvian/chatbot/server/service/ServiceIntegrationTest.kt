package eu.torvian.chatbot.server.service

//import arrow.core.getOrElse
//import eu.torvian.chatbot.server.domain.config.DatabaseConfig
//import eu.torvian.chatbot.server.domain.security.EncryptionConfig
//import eu.torvian.chatbot.server.koin.*
//import eu.torvian.chatbot.server.data.misc.Database
//import kotlinx.coroutines.runBlocking
//import org.junit.jupiter.api.AfterEach
//import org.junit.jupiter.api.BeforeEach
//import org.junit.jupiter.api.Test
//import org.koin.core.context.startKoin
//import org.koin.core.context.stopKoin
//import org.koin.test.KoinTest
//import org.koin.test.inject
//import kotlin.test.assertEquals
//import kotlin.test.assertTrue
//
///**
// * Integration test for the service layer.
// * Tests the complete flow from service interfaces down to the database.
// */
//class ServiceIntegrationTest : KoinTest {
//
//    private val sessionService: SessionService by inject()
//    private val groupService: GroupService by inject()
//    private val modelService: ModelService by inject()
//    private val messageService: MessageService by inject()
//
//    @BeforeEach
//    fun setup() {
//        val databaseConfig = DatabaseConfig(
//            type = "memory",
//            url = "jdbc:sqlite::memory:"
//        )
//
//        val encryptionConfig = EncryptionConfig(
//            keyVersion = 1,
//            masterKey = "test-master-key-32-characters-long"
//        )
//
//        startKoin {
//            modules(
//                configModule(databaseConfig, encryptionConfig),
//                databaseModule(),
//                miscModule(),
//                daoModule(),
//                serviceModule()
//            )
//        }
//
//        // Initialize database schema
//        val database = Database()
//        database.connect()
//        database.createSchema()
//    }
//
//    @AfterEach
//    fun teardown() {
//        stopKoin()
//    }
//
//    @Test
//    fun `test complete session and message flow`() = runBlocking {
//        // 1. Create a group
//        val group = groupService.createGroup("Test Group").getOrElse {
//            throw AssertionError("Failed to create group")
//        }
//        assertEquals("Test Group", group.name)
//
//        // 2. Create a session
//        val session = sessionService.createSession("Test Session").getOrElse {
//            throw AssertionError("Failed to create session")
//        }
//        assertEquals("Test Session", session.name)
//
//        // 3. Assign session to group
//        sessionService.updateSessionGroupId(session.id, group.id).getOrElse {
//            throw AssertionError("Failed to assign session to group")
//        }
//
//        // 4. Create a model
//        val model = modelService.addModel(
//            name = "Test Model",
//            baseUrl = "https://api.example.com",
//            type = "openai",
//            apiKey = "test-api-key"
//        ).getOrElse {
//            throw AssertionError("Failed to create model")
//        }
//        assertEquals("Test Model", model.name)
//
//        // 5. Create settings for the model
//        val settings = modelService.addSettings(
//            name = "Test Settings",
//            modelId = model.id,
//            systemMessage = "You are a helpful assistant",
//            temperature = 0.7f,
//            maxTokens = 1000,
//            customParamsJson = null
//        ).getOrElse {
//            throw AssertionError("Failed to create settings")
//        }
//        assertEquals("Test Settings", settings.name)
//
//        // 6. Configure session with model and settings
//        sessionService.updateSessionCurrentModelId(session.id, model.id).getOrElse {
//            throw AssertionError("Failed to set session model")
//        }
//        sessionService.updateSessionCurrentSettingsId(session.id, settings.id).getOrElse {
//            throw AssertionError("Failed to set session settings")
//        }
//
//        // 7. Send a message (this will create both user and assistant messages)
//        val messages = messageService.processNewMessage(
//            sessionId = session.id,
//            content = "Hello, how are you?",
//            parentMessageId = null
//        ).getOrElse {
//            throw AssertionError("Failed to process message")
//        }
//
//        assertEquals(2, messages.size)
//        assertEquals("user", messages[0].role)
//        assertEquals("Hello, how are you?", messages[0].content)
//        assertEquals("assistant", messages[1].role)
//        assertTrue(messages[1].content.contains("stubbed response"))
//
//        // 8. Verify session summaries include the group
//        val summaries = sessionService.getAllSessionsSummaries()
//        assertEquals(1, summaries.size)
//        assertEquals(group.name, summaries[0].groupName)
//
//        // 9. Verify messages are retrievable
//        val sessionMessages = messageService.getMessagesBySessionId(session.id)
//        assertEquals(2, sessionMessages.size)
//
//        println("✅ Service integration test completed successfully!")
//    }
//}
