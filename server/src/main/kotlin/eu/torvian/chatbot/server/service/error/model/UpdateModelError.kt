package eu.torvian.chatbot.server.service.error.model

/**
 * Represents possible errors when updating an existing LLM model.
 */
sealed interface UpdateModelError {
    /**
     * Indicates that the model with the specified ID was not found.
     * Maps from ModelError.ModelNotFound in the DAO layer.
     */
    data class ModelNotFound(val id: Long) : UpdateModelError
    /**
     * Indicates invalid input data for the update (e.g., name, baseUrl, type format).
     * This would typically be a business rule validation failure.
     */
    data class InvalidInput(val reason: String) : UpdateModelError
    /**
     * Indicates an error occurred while updating or deleting the API key.
     * Maps from ApiSecretError or CredentialManager specific errors.
     */
    data class CredentialError(val message: String) : UpdateModelError
}
