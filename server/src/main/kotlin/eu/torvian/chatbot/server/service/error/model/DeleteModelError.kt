package eu.torvian.chatbot.server.service.error.model

/**
 * Represents possible errors when deleting an LLM model.
 */
sealed interface DeleteModelError {
    /**
     * Indicates that the model with the specified ID was not found.
     * Maps from ModelError.ModelNotFound in the DAO layer.
     */
    data class ModelNotFound(val id: Long) : DeleteModelError
    /**
     * Indicates an error occurred while deleting the associated API key.
     * Maps from ApiSecretError or CredentialManager specific errors.
     */
    data class CredentialDeletionError(val message: String) : DeleteModelError
}
