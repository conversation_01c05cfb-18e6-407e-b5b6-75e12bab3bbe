package eu.torvian.chatbot.server.service
import arrow.core.Either
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.ModelSettings
import eu.torvian.chatbot.server.service.error.model.AddModelError
import eu.torvian.chatbot.server.service.error.model.UpdateModelError
import eu.torvian.chatbot.server.service.error.model.DeleteModelError
import eu.torvian.chatbot.server.service.error.model.GetModelError
import eu.torvian.chatbot.server.service.error.settings.GetSettingsByIdError // Settings errors are related but in 'settings' package
import eu.torvian.chatbot.server.service.error.settings.AddSettingsError
import eu.torvian.chatbot.server.service.error.settings.UpdateSettingsError
import eu.torvian.chatbot.server.service.error.settings.DeleteSettingsError


/**
 * Service interface for managing LLM Models and their Settings.
 */
interface ModelService {
    /**
     * Retrieves all LLM model configurations.
     */
    suspend fun getAllModels(): List<LLMModel>
    /**
     * Retrieves a single LLM model by its unique identifier.
     *
     * @param id The unique identifier of the LLM model to retrieve.
     * @return [Either] a [GetModelError.ModelNotFound] if the model doesn't exist, or the [LLMModel].
     */
    suspend fun getModelById(id: Long): Either<GetModelError.ModelNotFound, LLMModel>
    /**
     * Adds a new LLM model configuration.
     * Handles secure storage of the API key if provided.
     * @param name The display name for the model.
     * @param baseUrl The base URL for the LLM API.
     * @param type The type of LLM provider.
     * @param apiKey Optional raw API key string to be stored securely.
     * @return Either an [AddModelError] if model creation or key storage fails,
     *         or the newly created [LLMModel] (without the raw key).
     */
    suspend fun addModel(name: String, baseUrl: String, type: String, apiKey: String?): Either<AddModelError, LLMModel>
    /**
     * Updates an existing LLM model configuration.
     * Handles updating the API key if a new one is provided.
     * @param id The ID of the model to update.
     * @param name New display name (optional).
     * @param baseUrl New base URL (optional).
     * @param type New type (optional).
     * @param apiKey Optional new raw API key string to update the stored key.
     * @return Either an [UpdateModelError] if the model is not found, update fails, or key update fails,
     *         or the updated [LLMModel] (without the raw key).
     */
    suspend fun updateModel(id: Long, name: String?, baseUrl: String?, type: String?, apiKey: String?): Either<UpdateModelError, LLMModel>
    /**
     * Deletes an LLM model configuration.
     * Handles deletion of associated settings and the securely stored API key.
     * @param id The ID of the model to delete.
     * @return Either a [DeleteModelError] if the model doesn't exist or deletion fails, or Unit if successful.
     */
    suspend fun deleteModel(id: Long): Either<DeleteModelError, Unit>
    /**
     * Retrieves a specific settings profile by ID.
     * @param id The ID of the settings profile.
     * @return Either a [GetSettingsByIdError] if not found, or the [ModelSettings].
     */
    suspend fun getSettingsById(id: Long): Either<GetSettingsByIdError, ModelSettings>
    /**
     * Retrieves all settings profiles stored in the database.
     * @return A list of all [ModelSettings] objects.
     */
    suspend fun getAllSettings(): List<ModelSettings>
    /**
     * Retrieves all settings profiles associated with a specific LLM model.
     * @param modelId The ID of the LLM model.
     * @return A list of [ModelSettings] for the model, or an empty list if none exist.
     */
    suspend fun getSettingsByModelId(modelId: Long): List<ModelSettings>
    /**
     * Creates a new settings profile with the specified parameters.
     *
     * @param name The display name of the settings profile (e.g., "Default", "Creative")
     * @param modelId The ID of the LLM model this settings profile is associated with
     * @param systemMessage Optional system message/prompt to include in the conversation context
     * @param temperature Optional sampling temperature for text generation
     * @param maxTokens Optional maximum number of tokens to generate in the response
     * @param customParamsJson Optional model-specific parameters stored as a JSON string
     * @return [Either] an [AddSettingsError] if the associated model doesn't exist or insertion fails, or the newly created [ModelSettings]
     */
    suspend fun addSettings(
        name: String,
        modelId: Long,
        systemMessage: String?,
        temperature: Float?,
        maxTokens: Int?,
        customParamsJson: String?
    ): Either<AddSettingsError, ModelSettings>
    /**
     * Updates an existing settings profile with new values.
     *
     * @param id The ID of the settings profile to update.
     * @param name New name (optional).
     * @param systemMessage New system message (optional).
     * @param temperature New temperature (optional).
     * @param maxTokens New max tokens (optional).
     * @param customParamsJson New custom params JSON (optional).
     * @return [Either] an [UpdateSettingsError] if not found or update fails, or [Unit] on success
     */
    suspend fun updateSettings(
        id: Long, name: String?, systemMessage: String?, temperature: Float?,
        maxTokens: Int?, customParamsJson: String?
    ): Either<UpdateSettingsError, Unit>
    /**
     * Deletes a settings profile with the specified ID.
     *
     * @param id The unique identifier of the settings profile to delete
     * @return [Either] a [DeleteSettingsError] if not found, or [Unit] on success
     */
    suspend fun deleteSettings(id: Long): Either<DeleteSettingsError, Unit>

    /**
     * Checks if an API key is configured for a specific model.
     * @param modelId The ID of the model.
     * @return True if an API key ID is stored for the model, false otherwise.
     */
    suspend fun isApiKeyConfiguredForModel(modelId: Long): Boolean // Does not return Either, simple boolean check

}
