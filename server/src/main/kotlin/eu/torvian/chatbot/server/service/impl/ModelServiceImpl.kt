package eu.torvian.chatbot.server.service.impl

import arrow.core.Either
import arrow.core.*
import arrow.core.raise.*
import arrow.core.raise.ensure
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.ModelSettings
import eu.torvian.chatbot.server.data.dao.ModelDao
import eu.torvian.chatbot.server.data.dao.SettingsDao
import eu.torvian.chatbot.server.data.dao.error.ModelError
import eu.torvian.chatbot.server.data.dao.error.SettingsError
import eu.torvian.chatbot.server.service.ModelService
import eu.torvian.chatbot.server.service.error.model.*
import eu.torvian.chatbot.server.service.error.settings.*
import eu.torvian.chatbot.server.service.security.CredentialManager
import eu.torvian.chatbot.server.service.security.error.CredentialError
import eu.torvian.chatbot.server.utils.transactions.TransactionScope

/**
 * Implementation of the [ModelService] interface.
 */
class ModelServiceImpl(
    private val modelDao: ModelDao,
    private val settingsDao: SettingsDao,
    private val credentialManager: CredentialManager,
    private val transactionScope: TransactionScope,
) : ModelService {

    override suspend fun getAllModels(): List<LLMModel> {
        return transactionScope.transaction {
            modelDao.getAllModels()
        }
    }

    override suspend fun getModelById(id: Long): Either<GetModelError.ModelNotFound, LLMModel> =
        transactionScope.transaction {
            either {
                withError({ daoError: ModelError.ModelNotFound ->
                    GetModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.getModelById(id).bind()
                }
            }
        }

    override suspend fun addModel(name: String, baseUrl: String, type: String, apiKey: String?): Either<AddModelError, LLMModel> =
        transactionScope.transaction {
            either {
                ensure(!name.isBlank()) { AddModelError.InvalidInput("Model name cannot be blank.") }
                ensure(!baseUrl.isBlank()) { AddModelError.InvalidInput("Model base URL cannot be blank.") }
                ensure(!type.isBlank()) { AddModelError.InvalidInput("Model type cannot be blank.") }

                val apiKeyId: String? = if (!apiKey.isNullOrBlank()) {
                    credentialManager.storeCredential(apiKey)
                } else {
                    null
                }

                modelDao.insertModel(name, baseUrl, type, apiKeyId)
            }
        }

    override suspend fun updateModel(id: Long, name: String?, baseUrl: String?, type: String?, apiKey: String?): Either<UpdateModelError, LLMModel> =
        transactionScope.transaction {
            either {
                ensure(!(name != null && name.isBlank())) {
                    UpdateModelError.InvalidInput("Name cannot be blank.")
                }
                ensure(!(baseUrl != null && baseUrl.isBlank())) {
                    UpdateModelError.InvalidInput("Base URL cannot be blank.")
                }
                ensure(!(type != null && type.isBlank())) {
                    UpdateModelError.InvalidInput("Type cannot be blank.")
                }

                val existingModel = withError({ daoError: ModelError.ModelNotFound ->
                    UpdateModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.getModelById(id).bind()
                }

                var apiKeyIdToUpdate = existingModel.apiKeyId

                if (!apiKey.isNullOrBlank()) {
                    existingModel.apiKeyId?.let { oldKeyId ->
                        withError({ deleteErr: CredentialError.CredentialNotFound ->
                            UpdateModelError.CredentialError("Associated API key not found during deletion attempt for old key.")
                        }) {
                            credentialManager.deleteCredential(oldKeyId).bind()
                        }
                    }

                    apiKeyIdToUpdate = credentialManager.storeCredential(apiKey)
                }

                val updatedModel = existingModel.copy(
                    name = name ?: existingModel.name,
                    baseUrl = baseUrl ?: existingModel.baseUrl,
                    type = type ?: existingModel.type,
                    apiKeyId = apiKeyIdToUpdate
                )

                withError({ daoError: ModelError.ModelNotFound ->
                    UpdateModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.updateModel(updatedModel).bind()
                }

                updatedModel
            }
        }

    override suspend fun deleteModel(id: Long): Either<DeleteModelError, Unit> =
        transactionScope.transaction {
            either {
                val modelToDelete = withError({ daoError: ModelError.ModelNotFound ->
                    DeleteModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.getModelById(id).bind()
                }

                modelToDelete.apiKeyId?.let { keyId ->
                    withError({ deleteErr: CredentialError.CredentialNotFound ->
                        DeleteModelError.CredentialDeletionError("Associated API key not found during deletion attempt.")
                    }) {
                        credentialManager.deleteCredential(keyId).bind()
                    }
                }

                withError({ daoError: ModelError.ModelNotFound ->
                    DeleteModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.deleteModel(id).bind()
                }
            }
        }

    override suspend fun getSettingsById(id: Long): Either<GetSettingsByIdError, ModelSettings> =
        transactionScope.transaction {
            either {
                withError({ daoError: SettingsError.SettingsNotFound ->
                    GetSettingsByIdError.SettingsNotFound(daoError.id)
                }) {
                    settingsDao.getSettingsById(id).bind()
                }
            }
        }

    override suspend fun getAllSettings(): List<ModelSettings> {
        return transactionScope.transaction {
            settingsDao.getAllSettings()
        }
    }

    override suspend fun getSettingsByModelId(modelId: Long): List<ModelSettings> {
        return transactionScope.transaction {
            settingsDao.getSettingsByModelId(modelId)
        }
    }

    override suspend fun addSettings(
        name: String, modelId: Long, systemMessage: String?,
        temperature: Float?, maxTokens: Int?, customParamsJson: String?
    ): Either<AddSettingsError, ModelSettings> =
        transactionScope.transaction {
            either {
                ensure(!name.isBlank()) {
                    AddSettingsError.InvalidInput("Settings name cannot be blank.")
                }
                ensure(!(temperature != null && (temperature < 0f || temperature > 2f))) {
                    AddSettingsError.InvalidInput("Temperature must be between 0.0 and 2.0")
                }
                ensure(!(maxTokens != null && maxTokens <= 0)) {
                    AddSettingsError.InvalidInput("Max tokens must be positive")
                }

                withError({ daoError: SettingsError.ModelNotFound ->
                    AddSettingsError.ModelNotFound(daoError.modelId)
                }) {
                    settingsDao.insertSettings(name, modelId, systemMessage, temperature, maxTokens, customParamsJson).bind()
                }
            }
        }

    override suspend fun updateSettings(
        id: Long, name: String?, systemMessage: String?, temperature: Float?,
        maxTokens: Int?, customParamsJson: String?
    ): Either<UpdateSettingsError, Unit> =
        transactionScope.transaction {
            either {
                ensure(!(name != null && name.isBlank())) {
                    UpdateSettingsError.InvalidInput("Settings name cannot be blank.")
                }
                ensure(!(temperature != null && (temperature < 0f || temperature > 2f))) {
                    UpdateSettingsError.InvalidInput("Temperature must be between 0.0 and 2.0")
                }
                ensure(!(maxTokens != null && maxTokens <= 0)) {
                    UpdateSettingsError.InvalidInput("Max tokens must be positive")
                }

                val existingSettings = withError({ daoError: SettingsError.SettingsNotFound ->
                    UpdateSettingsError.SettingsNotFound(daoError.id)
                }) {
                    settingsDao.getSettingsById(id).bind()
                }

                val updatedSettings = existingSettings.copy(
                    name = name ?: existingSettings.name,
                    systemMessage = systemMessage ?: existingSettings.systemMessage,
                    temperature = temperature ?: existingSettings.temperature,
                    maxTokens = maxTokens ?: existingSettings.maxTokens,
                    customParamsJson = customParamsJson ?: existingSettings.customParamsJson
                )
                withError({ daoError: SettingsError ->
                    when(daoError) {
                        is SettingsError.SettingsNotFound -> UpdateSettingsError.SettingsNotFound(daoError.id)
                        is SettingsError.ModelNotFound -> UpdateSettingsError.InvalidInput("Model associated with settings not found during update")
                    }
                }) {
                    settingsDao.updateSettings(updatedSettings).bind()
                }
            }
        }

    override suspend fun deleteSettings(id: Long): Either<DeleteSettingsError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: SettingsError.SettingsNotFound ->
                    DeleteSettingsError.SettingsNotFound(daoError.id)
                }) {
                    settingsDao.deleteSettings(id).bind()
                }
            }
        }

    override suspend fun isApiKeyConfiguredForModel(modelId: Long): Boolean {
        return transactionScope.transaction {
            modelDao.getModelById(modelId)
                .map { it.apiKeyId != null }
                .getOrElse { false }
        }
    }
}
